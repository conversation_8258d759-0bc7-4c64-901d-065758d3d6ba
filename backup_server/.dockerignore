# Ignore compiled binaries
backup-server
backup_server

# Ignore data directory (will be mounted as volume)
data/

# Ignore git files
.git/
.gitignore

# Ignore IDE files
.vscode/
.idea/
*.swp
*.swo

# Ignore OS files
.DS_Store
Thumbs.db

# Ignore temporary files
*.tmp
*.temp
*.log

# Ignore documentation that's not needed in container
README.md
MIGRATION_SUMMARY.md
*.md

# Ignore example files
*.example

# Keep only essential files for Docker build:
# - Source code (*.go)
# - Go modules (go.mod, go.sum)
# - Web assets (web/)
# - Scripts (scripts/)
# - Dockerfile
# - docker-compose.yml
