package main

import (
	"backup_server/pkg/config"
	"backup_server/pkg/handlers"
	"backup_server/pkg/models"
	"backup_server/pkg/services"
	"backup_server/pkg/utils"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
)

func main() {
	// 从环境变量加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 设置代理环境变量
	if cfg.Proxy.HTTPProxy != "" {
		os.Setenv("http_proxy", cfg.Proxy.HTTPProxy)
	}
	if cfg.Proxy.HTTPSProxy != "" {
		os.Setenv("https_proxy", cfg.Proxy.HTTPSProxy)
	}
	if cfg.Proxy.AllProxy != "" {
		os.Setenv("all_proxy", cfg.Proxy.AllProxy)
	}

	// 确保数据目录存在
	if err := utils.EnsureDir(cfg.Storage.DataPath); err != nil {
		log.Fatalf("创建数据目录失败: %v", err)
	}

	// 创建后端客户端
	backendClient := services.NewBackendClient(cfg)

	// 测试后端连接
	log.Println("测试后端连接...")
	if err := backendClient.TestConnection(); err != nil {
		log.Printf("警告: 后端连接失败: %v", err)
	} else {
		log.Println("后端连接正常")
	}

	// 获取MinIO配置
	log.Println("获取MinIO配置...")
	minioConfig, err := backendClient.GetMinIOConfig()
	if err != nil {
		log.Printf("警告: 获取MinIO配置失败: %v", err)
		log.Println("使用默认MinIO配置")
		// 使用默认配置
		minioConfig = &models.MinIOConfig{
			Endpoint:  "localhost:9000",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin",
			UseSSL:    false,
		}
	}
	log.Printf("MinIO配置: %s", minioConfig.Endpoint)

	// 创建下载服务
	downloadService, err := services.NewDownloadService(cfg, minioConfig)
	if err != nil {
		log.Fatalf("创建下载服务失败: %v", err)
	}

	// 创建存储服务
	storageService := services.NewStorageService(cfg)

	// 创建转换服务
	conversionService := services.NewConversionService(storageService)
	defer conversionService.Stop()

	// 创建处理器
	handler := handlers.NewHandler(backendClient, downloadService, storageService, conversionService)

	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建路由
	r := gin.Default()

	// 静态文件服务
	r.Static("/static", "./web/static")
	r.LoadHTMLGlob("web/templates/*")

	// API路由
	api := r.Group("/api")
	{
		// 健康检查
		api.GET("/health", handler.Health)
		
		// 连接测试
		api.GET("/test", handler.TestConnection)

		// 视频管理
		videos := api.Group("/videos")
		{
			videos.GET("/remote", handler.GetVideoList)     // 获取云端视频列表
			videos.POST("/download", handler.DownloadVideos) // 下载视频
			videos.GET("/local", handler.GetLocalVideos)    // 获取本地视频列表
			videos.GET("/local/ids", handler.GetLocalVideoIds) // 获取本地视频ID列表
			videos.GET("/local/cat/:cat_id", handler.GetLocalCatVideoList) // 获取本地猫咪视频列表
			videos.DELETE("/:id", handler.DeleteVideo)      // 删除本地视频
		}

		// 任务管理
		tasks := api.Group("/tasks")
		{
			tasks.GET("", handler.GetAllTasks)           // 获取所有任务（支持筛选和分页）
			tasks.GET("/stats", handler.GetTaskStats)    // 获取任务状态统计
			tasks.GET("/:id", handler.GetTaskStatus)     // 获取任务状态
			tasks.DELETE("/:id", handler.CancelTask)     // 取消任务
			tasks.POST("/:id/pause", handler.PauseTask)  // 暂停任务
			tasks.POST("/:id/resume", handler.ResumeTask) // 恢复任务
			tasks.POST("/:id/retry", handler.RetryTask)  // 重试任务
			tasks.POST("/:id/cancel", handler.CancelTask) // 取消任务
			tasks.POST("/clear-completed", handler.ClearCompletedTasks) // 清理已完成任务
			tasks.POST("/retry-failed", handler.RetryFailedTasks)       // 重试失败任务
			tasks.POST("/pause-all", handler.PauseAllTasks)             // 暂停所有任务
			tasks.POST("/resume-all", handler.ResumeAllTasks)           // 恢复所有任务
		}

		// 转换任务管理
		conversions := api.Group("/conversions")
		{
			conversions.POST("/convert", handler.ConvertVideos)        // 转换指定视频
			conversions.POST("/convert-all", handler.ConvertAllVideos) // 转换所有未转换视频
			conversions.GET("/stats", handler.GetConversionStats)      // 获取转换任务状态统计
			conversions.GET("", handler.GetAllConversions)             // 获取所有转换任务（支持筛选和分页）
		}

		// 存储管理
		storage := api.Group("/storage")
		{
			storage.GET("/stats", handler.GetStorageStats)   // 获取存储统计
			storage.POST("/cleanup", handler.CleanupStorage) // 清理存储
		}

		// 猫咪管理
		cats := api.Group("/cats")
		{
			cats.GET("", handler.GetCatList)                    // 获取猫咪列表
			cats.GET("/:cat_id/videos", handler.GetCatVideoList) // 获取猫咪的视频列表
		}
	}

	// Web界面路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "视频备份系统",
		})
	})

	// 启动服务器
	addr := fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port)
	log.Printf("启动服务器: http://%s", addr)
	log.Printf("数据存储路径: %s", cfg.Storage.DataPath)
	log.Printf("最大并发下载: %d", cfg.Download.MaxConcurrent)

	if err := r.Run(addr); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}
