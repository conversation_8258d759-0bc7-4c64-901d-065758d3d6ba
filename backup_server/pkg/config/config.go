package config

import (
	"fmt"
	"os"
	"strconv"
)

// Config 应用配置
type Config struct {
	Server struct {
		Port string
		Host string
	}

	Backend struct {
		URL       string
		AuthToken string
	}

	MinIO struct {
		Endpoint  string
		AccessKey string
		SecretKey string
		UseSSL    bool
		Bucket    string
	}

	Storage struct {
		DataPath string
	}

	Download struct {
		MaxConcurrent int
		ChunkSize     int
	}

	Proxy struct {
		HTTPProxy  string
		HTTPSProxy string
		AllProxy   string
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取环境变量并转换为整数，如果不存在或转换失败则返回默认值
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvBool 获取环境变量并转换为布尔值，如果不存在或转换失败则返回默认值
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// LoadConfig 从环境变量加载配置
func LoadConfig() (*Config, error) {
	config := &Config{}

	// 服务器配置
	config.Server.Host = getEnv("SERVER_HOST", "0.0.0.0")
	config.Server.Port = getEnv("SERVER_PORT", "8080")

	// 后端服务配置
	config.Backend.URL = getEnv("BACKEND_URL", "")
	config.Backend.AuthToken = getEnv("BACKEND_AUTH_TOKEN", "")

	// MinIO配置（备用配置）
	config.MinIO.Endpoint = getEnv("MINIO_ENDPOINT", "localhost:9000")
	config.MinIO.AccessKey = getEnv("MINIO_ACCESS_KEY", "minioadmin")
	config.MinIO.SecretKey = getEnv("MINIO_SECRET_KEY", "minioadmin")
	config.MinIO.UseSSL = getEnvBool("MINIO_USE_SSL", false)
	config.MinIO.Bucket = getEnv("MINIO_BUCKET", "records")

	// 存储配置
	config.Storage.DataPath = getEnv("DATA_PATH", "./data")

	// 下载配置
	config.Download.MaxConcurrent = getEnvInt("MAX_CONCURRENT_DOWNLOADS", 5)
	config.Download.ChunkSize = getEnvInt("DOWNLOAD_CHUNK_SIZE", 1024*1024) // 1MB

	// 代理配置
	config.Proxy.HTTPProxy = getEnv("PROXY_HTTP", "")
	config.Proxy.HTTPSProxy = getEnv("PROXY_HTTPS", "")
	config.Proxy.AllProxy = getEnv("PROXY_ALL", "")

	// 验证必要的配置
	if config.Backend.URL == "" {
		return nil, fmt.Errorf("BACKEND_URL 环境变量是必需的")
	}
	if config.Backend.AuthToken == "" {
		return nil, fmt.Errorf("BACKEND_AUTH_TOKEN 环境变量是必需的")
	}

	return config, nil
}
