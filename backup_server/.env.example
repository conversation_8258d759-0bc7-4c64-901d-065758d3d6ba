# Backup Server Configuration
TZ=Asia/Shanghai

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Storage Configuration
DATA_PATH=./data

# Download Configuration
MAX_CONCURRENT_DOWNLOADS=5
DOWNLOAD_CHUNK_SIZE=1048576

# Backend Server Connection (required)
BACKEND_URL=https://api.caby.care
BACKEND_AUTH_TOKEN=FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K

# MinIO Configuration (will be fetched from backend, these are fallback values)
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_USE_SSL=false
MINIO_BUCKET=records

# Proxy Configuration
# For Docker containers: use host.docker.internal
# For local development: use 127.0.0.1
PROXY_HTTP=http://host.docker.internal:7897
PROXY_HTTPS=http://host.docker.internal:7897
PROXY_ALL=socks5://host.docker.internal:7897
http_proxy=http://host.docker.internal:7897
https_proxy=http://host.docker.internal:7897
HTTP_PROXY=http://host.docker.internal:7897
HTTPS_PROXY=http://host.docker.internal:7897
no_proxy=localhost,127.0.0.1,host.docker.internal
NO_PROXY=localhost,127.0.0.1,host.docker.internal

# Runtime Proxy Configuration (for application)
APP_HTTP_PROXY=http://host.docker.internal:7897
APP_HTTPS_PROXY=http://host.docker.internal:7897
