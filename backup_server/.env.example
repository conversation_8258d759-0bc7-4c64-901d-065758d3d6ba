# Backup Server Configuration
TZ=Asia/Shanghai

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Storage Configuration
DATA_PATH=./data

# Download Configuration
MAX_CONCURRENT_DOWNLOADS=5
DOWNLOAD_CHUNK_SIZE=1048576

# Backend Server Connection (required)
BACKEND_URL=https://api.caby.care
BACKEND_AUTH_TOKEN=FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K

# MinIO Configuration (will be fetched from backend, these are fallback values)
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_USE_SSL=false
MINIO_BUCKET=records
