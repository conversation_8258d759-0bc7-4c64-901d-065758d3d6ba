# 配置迁移总结

## 概述

已成功将 backup_server 的配置系统从 `config.yaml` 文件迁移到 `.env` 环境变量配置。

## 主要更改

### 1. 删除的文件
- `config.yaml` - 原配置文件
- `config.yaml.example` - 配置文件模板
- `config/` - 空配置目录

### 2. 修改的文件

#### `pkg/config/config.go`
- 移除了 `gopkg.in/yaml.v3` 依赖
- 重写了 `LoadConfig()` 函数，改为从环境变量读取配置
- 添加了辅助函数：`getEnv()`, `getEnvInt()`, `getEnvBool()`
- 移除了 YAML 结构标签

#### `main.go`
- 更新配置加载调用：`config.LoadConfig()` (移除了参数)

#### `go.mod`
- 移除了 `gopkg.in/yaml.v3` 依赖

#### `docker-compose.yml`
- 移除了 `./config:/app/config` 卷挂载

#### `scripts/quick_deploy.sh`
- 更新必需文件检查：将 `config.yaml` 改为 `.env`
- 更新默认 `.env` 文件创建内容，使用新的环境变量名称

#### `README.md`
- 更新配置说明，从 `config.yaml` 改为 `.env` 文件
- 更新故障排除部分的配置文件引用

#### `.env` 和 `.env.example`
- 添加了完整的环境变量配置
- 使用新的环境变量命名规范

## 环境变量映射

### 原 config.yaml 结构 → 新环境变量

```yaml
# 原配置结构
server:
  host: "0.0.0.0"           → SERVER_HOST=0.0.0.0
  port: "8080"              → SERVER_PORT=8080

backend:
  url: "https://..."        → BACKEND_URL=https://...
  auth_token: "..."         → BACKEND_AUTH_TOKEN=...

storage:
  data_path: "./data"       → DATA_PATH=./data

download:
  max_concurrent: 5         → MAX_CONCURRENT_DOWNLOADS=5
  chunk_size: 1048576       → DOWNLOAD_CHUNK_SIZE=1048576

minio:
  endpoint: "..."           → MINIO_ENDPOINT=...
  access_key: "..."         → MINIO_ACCESS_KEY=...
  secret_key: "..."         → MINIO_SECRET_KEY=...
  use_ssl: false            → MINIO_USE_SSL=false
  bucket: "records"         → MINIO_BUCKET=records

proxy:
  http_proxy: "..."         → PROXY_HTTP=...
  https_proxy: "..."        → PROXY_HTTPS=...
  all_proxy: "..."          → PROXY_ALL=...
```

## 新的配置方式

### 必需环境变量
- `BACKEND_URL`: 后端API服务器地址
- `BACKEND_AUTH_TOKEN`: 后端服务认证令牌

### 可选环境变量（有默认值）
- `SERVER_HOST`: 服务监听地址（默认: 0.0.0.0）
- `SERVER_PORT`: 服务端口（默认: 8080）
- `DATA_PATH`: 数据存储路径（默认: ./data）
- `MAX_CONCURRENT_DOWNLOADS`: 最大并发下载数（默认: 5）
- `DOWNLOAD_CHUNK_SIZE`: 下载块大小（默认: 1048576）
- `MINIO_*`: MinIO配置（有默认值，通常从后端API获取）
- `PROXY_*`: 代理配置（可选）

## 使用方法

### 1. 直接设置环境变量
```bash
export BACKEND_URL=https://api.caby.care
export BACKEND_AUTH_TOKEN=your-token
./backup-server
```

### 2. 使用 .env 文件
```bash
# 编辑 .env 文件
vim .env

# 运行服务（Docker会自动加载.env文件）
./scripts/quick_deploy.sh
```

### 3. Docker Compose
Docker Compose 会自动加载 `.env` 文件中的环境变量。

## 验证

配置迁移已通过以下方式验证：
1. ✅ Go 代码编译成功
2. ✅ 环境变量正确加载
3. ✅ 程序能够读取配置并尝试连接后端服务
4. ✅ Docker 构建和部署脚本更新完成

## 优势

1. **简化配置**: 不再需要维护 YAML 配置文件
2. **环境友好**: 更符合 12-factor 应用原则
3. **Docker 原生**: 与 Docker/Kubernetes 环境变量管理更好集成
4. **安全性**: 敏感信息可以通过环境变量注入，不需要存储在文件中
5. **部署简化**: 减少了配置文件管理的复杂性

## 注意事项

1. 确保在部署前正确设置必需的环境变量
2. 敏感信息（如认证令牌）应通过安全的方式管理
3. 在生产环境中，建议使用 Kubernetes Secrets 或类似机制管理环境变量
