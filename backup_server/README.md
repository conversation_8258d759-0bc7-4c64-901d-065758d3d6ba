# 视频备份服务器 (Backup Server)

## 概述

视频备份服务器是一个基于Go语言开发的Web应用程序，用于从后端服务器下载和管理视频文件。该服务提供了一个直观的Web界面，支持视频文件的批量下载、分类管理和存储统计功能。

## 主要功能

- **视频文件下载**: 从后端服务器批量下载视频文件
- **分类管理**: 按设备和类别组织视频文件
- **存储统计**: 实时显示存储空间使用情况
- **Web界面**: 提供友好的Web管理界面
- **并发下载**: 支持多线程并发下载，提高效率
- **代理支持**: 支持HTTP/HTTPS/SOCKS5代理配置

## 系统要求

### 软件依赖
- **Docker**: 用于容器化部署
- **Docker Compose**: 用于服务编排
- **Go 1.19+**: 用于编译Go程序（如果需要本地构建）

### 硬件要求
- **CPU**: 支持x86_64或ARM64架构
- **内存**: 建议至少512MB可用内存
- **存储**: 根据视频文件大小预留足够空间
- **网络**: 稳定的网络连接用于下载视频文件

## 快速开始

### 1. 获取代码
```bash
# 进入backup_server目录
cd backup_server
```

### 2. 配置服务
服务使用 `.env` 文件进行配置。部署脚本会自动创建默认的 `.env` 文件，您也可以手动编辑：

主要配置项：
- `BACKEND_URL`: 后端服务器地址
- `BACKEND_AUTH_TOKEN`: 认证令牌
- `DATA_PATH`: 本地存储路径
- `MAX_CONCURRENT_DOWNLOADS`: 最大并发下载数

### 3. 一键部署
运行快速部署脚本：
```bash
./scripts/quick_deploy.sh
```

### 4. 访问服务
部署完成后，在浏览器中访问：
```
http://127.0.0.1:8080
```

## 详细配置说明

### 环境变量配置 (.env)

#### 服务器配置
```bash
SERVER_HOST=0.0.0.0    # 监听地址，0.0.0.0表示所有网卡
SERVER_PORT=8080       # 服务端口
```

#### 后端服务配置
```bash
BACKEND_URL=https://api.caby.care              # 后端服务器地址
BACKEND_AUTH_TOKEN=your-auth-token             # 认证令牌
```

#### 存储配置
```bash
DATA_PATH=./data       # 本地数据存储路径
```

#### 下载配置
```bash
MAX_CONCURRENT_DOWNLOADS=5    # 最大并发下载数
DOWNLOAD_CHUNK_SIZE=1048576   # 下载块大小(1MB)
```

#### MinIO配置（备用配置）
```bash
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_USE_SSL=false
MINIO_BUCKET=records
```

#### 代理配置（可选）
```bash
PROXY_HTTP=http://127.0.0.1:7897
PROXY_HTTPS=http://127.0.0.1:7897
PROXY_ALL=socks5://127.0.0.1:7897
```

### 配置文件说明

#### 必需配置项
- `BACKEND_URL`: 后端API服务器地址
- `BACKEND_AUTH_TOKEN`: 后端服务认证令牌

#### 可选配置项
- `SERVER_HOST`: 服务监听地址（默认: 0.0.0.0）
- `SERVER_PORT`: 服务端口（默认: 8080）
- `DATA_PATH`: 数据存储路径（默认: ./data）
- `MAX_CONCURRENT_DOWNLOADS`: 最大并发下载数（默认: 5）
- `DOWNLOAD_CHUNK_SIZE`: 下载块大小（默认: 1048576）

#### 代理配置（如不需要可留空）
- `PROXY_HTTP`: HTTP代理地址
- `PROXY_HTTPS`: HTTPS代理地址
- `PROXY_ALL`: 通用代理地址（SOCKS5等）

## 部署脚本选项

`quick_deploy.sh` 脚本支持以下选项：

```bash
./scripts/quick_deploy.sh [选项]
```

### 可用选项
- `--skip-build`: 跳过构建步骤
- `--logs`: 部署后跟踪日志输出
- `--no-clean`: 跳过Docker清理（更快但占用更多磁盘）
- `--force-clean`: 强制完整清理（默认行为）
- `-h, --help`: 显示帮助信息

### 使用示例
```bash
# 标准部署
./scripts/quick_deploy.sh

# 跳过构建并查看日志
./scripts/quick_deploy.sh --skip-build --logs

# 快速部署（不清理）
./scripts/quick_deploy.sh --no-clean
```

## 日志管理

### 查看日志
使用提供的日志脚本：
```bash
./scripts/logs.sh
```

或直接使用Docker命令：
```bash
# 查看实时日志
docker-compose logs -f

# 查看最近100行日志
docker-compose logs --tail=100
```

### 日志文件位置
- 容器内日志：通过Docker日志系统管理
- 应用日志：输出到标准输出，由Docker收集

## 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :8080

# 或使用netstat
netstat -tulpn | grep 8080
```

#### 2. Docker服务未启动
```bash
# 启动Docker服务
sudo systemctl start docker

# 检查Docker状态
docker info
```

#### 3. 构建失败
```bash
# 检查Go环境
go version

# 清理并重新构建
./scripts/quick_deploy.sh --force-clean
```

#### 4. 后端连接失败
- 检查 `.env` 文件中的 `BACKEND_URL` 和 `BACKEND_AUTH_TOKEN`
- 确认后端服务器正在运行
- 验证网络连接和防火墙设置

### 调试模式
如需详细调试信息，可以：
1. 查看容器日志：`docker-compose logs -f backup-server`
2. 进入容器：`docker-compose exec backup-server sh`
3. 检查环境变量：确认 `.env` 文件配置正确

## 服务管理

### 启动服务
```bash
docker-compose up -d
```

### 停止服务
```bash
docker-compose down
```

### 重启服务
```bash
docker-compose restart
```

### 查看服务状态
```bash
docker-compose ps
```

## 数据管理

### 数据目录结构
```
data/
├── by_device/     # 按设备分类的视频文件
└── by_cat/        # 按类别分类的视频文件
```

### 备份数据
建议定期备份 `data` 目录中的重要文件：
```bash
# 创建备份
tar -czf backup_$(date +%Y%m%d).tar.gz data/

# 恢复备份
tar -xzf backup_20240101.tar.gz
```

## 性能优化

### 下载性能
- 调整 `download.max_concurrent` 参数
- 根据网络带宽调整 `download.chunk_size`
- 确保有足够的磁盘空间

### 系统资源
- 监控内存使用情况
- 定期清理不需要的视频文件
- 使用SSD存储提高I/O性能

## 安全注意事项

1. **网络安全**: 建议在内网环境中使用
2. **访问控制**: 配置防火墙限制访问来源
3. **数据保护**: 定期备份重要数据
4. **认证安全**: 保护好后端服务的认证令牌

## 技术支持

如遇到问题，请：
1. 查看日志文件获取错误信息
2. 检查配置文件是否正确
3. 确认系统环境满足要求
4. 参考故障排除章节

---

**注意**: 本服务主要用于内部视频文件管理，请确保在安全的网络环境中使用。
